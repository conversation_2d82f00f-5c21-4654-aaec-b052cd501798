/**
 * 图片上传管理器
 * 负责图片上传、预览、删除和Gemini Vision API集成
 * <AUTHOR>
 * @version 1.0.0
 */

// 获取依赖模块（延迟获取以确保加载顺序）
function getLogger() {
    return window.OTA && window.OTA.logger || window.logger;
}

class ImageUploadManager {
    constructor() {
        this.maxFileSize = 5 * 1024 * 1024; // 5MB
        this.allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        this.uploadedImages = [];
        this.isProcessing = false;
        this.init();
    }

    /**
     * 初始化图片上传管理器
     */
    init() {
        this.bindEvents();
        const logger = getLogger();
        if (logger) {
            logger.log('图片上传管理器已初始化', 'info');
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 文件输入变化事件
        const fileInput = document.getElementById('imageFileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }

        // 上传区域点击事件
        const uploadArea = document.getElementById('imageUploadArea');
        if (uploadArea) {
            uploadArea.addEventListener('click', () => this.triggerFileSelect());
            uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
            uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        }
    }

    /**
     * 触发文件选择
     */
    triggerFileSelect() {
        const fileInput = document.getElementById('imageFileInput');
        if (fileInput) {
            fileInput.click();
        }
    }

    /**
     * 处理文件选择
     * @param {Event} event - 文件选择事件
     */
    handleFileSelect(event) {
        const files = event.target.files;
        if (files && files.length > 0) {
            this.processFiles(Array.from(files));
        }
    }

    /**
     * 处理拖拽悬停
     * @param {DragEvent} event - 拖拽事件
     */
    handleDragOver(event) {
        event.preventDefault();
        event.stopPropagation();
        
        const uploadArea = document.getElementById('imageUploadArea');
        if (uploadArea) {
            uploadArea.classList.add('drag-over');
        }
    }

    /**
     * 处理拖拽离开
     * @param {DragEvent} event - 拖拽事件
     */
    handleDragLeave(event) {
        event.preventDefault();
        event.stopPropagation();
        
        const uploadArea = document.getElementById('imageUploadArea');
        if (uploadArea) {
            uploadArea.classList.remove('drag-over');
        }
    }

    /**
     * 处理文件拖放
     * @param {DragEvent} event - 拖放事件
     */
    handleDrop(event) {
        event.preventDefault();
        event.stopPropagation();
        
        const uploadArea = document.getElementById('imageUploadArea');
        if (uploadArea) {
            uploadArea.classList.remove('drag-over');
        }

        const files = event.dataTransfer.files;
        if (files && files.length > 0) {
            this.processFiles(Array.from(files));
        }
    }

    /**
     * 处理文件列表
     * @param {File[]} files - 文件列表
     */
    async processFiles(files) {
        for (const file of files) {
            if (this.validateFile(file)) {
                await this.uploadImage(file);
            }
        }
    }

    /**
     * 验证文件
     * @param {File} file - 文件对象
     * @returns {boolean} 是否有效
     */
    validateFile(file) {
        // 检查文件类型
        if (!this.allowedTypes.includes(file.type)) {
            const logger = getLogger();
            if (logger) {
                logger.log(`不支持的文件类型: ${file.type}`, 'error');
            }
            window.uiManager?.showAlert(`不支持的文件类型: ${file.type}`, 'error');
            return false;
        }

        // 检查文件大小
        if (file.size > this.maxFileSize) {
            const sizeMB = (file.size / (1024 * 1024)).toFixed(2);
            const logger = getLogger();
            if (logger) {
                logger.log(`文件过大: ${sizeMB}MB，最大支持5MB`, 'error');
            }
            window.uiManager?.showAlert(`文件过大: ${sizeMB}MB，最大支持5MB`, 'error');
            return false;
        }

        return true;
    }

    /**
     * 上传图片
     * @param {File} file - 图片文件
     */
    async uploadImage(file) {
        try {
            this.isProcessing = true;
            this.updateUploadStatus('正在处理图片...', 'info');

            // 创建图片对象
            const imageData = {
                id: this.generateImageId(),
                file: file,
                name: file.name,
                size: file.size,
                type: file.type,
                uploadTime: new Date().toISOString(),
                base64: null,
                extractedText: null,
                status: 'uploading'
            };

            // 转换为base64
            imageData.base64 = await this.fileToBase64(file);
            imageData.status = 'uploaded';

            // 添加到上传列表
            this.uploadedImages.push(imageData);

            // 更新UI
            this.renderImagePreview(imageData);
            this.updateUploadStatus('图片上传成功', 'success');

            getLogger().log(`图片上传成功: ${file.name}`, 'success');

            // 自动开始图片分析
            await this.analyzeImage(imageData);

        } catch (error) {
            getLogger().log(`图片上传失败: ${error.message}`, 'error');
            window.uiManager?.showAlert(`图片上传失败: ${error.message}`, 'error');
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * 文件转base64
     * @param {File} file - 文件对象
     * @returns {Promise<string>} base64字符串
     */
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * 分析图片内容
     * @param {Object} imageData - 图片数据
     */
    async analyzeImage(imageData) {
        try {
            imageData.status = 'analyzing';
            this.updateImageStatus(imageData.id, 'analyzing');
            this.updateUploadStatus('正在分析图片内容...', 'info');

            // 调用Gemini Vision API
            const geminiService = window.OTA?.geminiService || window.getGeminiService?.();
            if (!geminiService) {
                throw new Error('Gemini服务未初始化');
            }

            const extractedText = await geminiService.analyzeImage(imageData.base64);
            
            if (extractedText && extractedText.trim()) {
                imageData.extractedText = extractedText;
                imageData.status = 'analyzed';
                
                // 自动填入订单描述框
                this.fillOrderDescription(extractedText);
                
                this.updateImageStatus(imageData.id, 'analyzed');
                this.updateUploadStatus('图片分析完成', 'success');
                
                getLogger().log(`图片分析成功: ${imageData.name}`, 'success');
            } else {
                imageData.status = 'no-text';
                this.updateImageStatus(imageData.id, 'no-text');
                this.updateUploadStatus('未检测到文本内容', 'warning');
            }

        } catch (error) {
            imageData.status = 'error';
            this.updateImageStatus(imageData.id, 'error');
            this.updateUploadStatus(`分析失败: ${error.message}`, 'error');
            getLogger().log(`图片分析失败: ${error.message}`, 'error');
        }
    }

    /**
     * 填入订单描述
     * @param {string} text - 提取的文本
     */
    fillOrderDescription(text) {
        const orderInput = document.getElementById('orderInput');
        if (orderInput) {
            const currentText = orderInput.value.trim();
            const newText = currentText ? `${currentText}\n\n${text}` : text;
            orderInput.value = newText;
            
            // 触发输入事件以启动实时分析
            orderInput.dispatchEvent(new Event('input', { bubbles: true }));
            
            getLogger().log('已将提取的文本填入订单描述框', 'info');
        }
    }

    /**
     * 渲染图片预览
     * @param {Object} imageData - 图片数据
     */
    renderImagePreview(imageData) {
        const container = document.getElementById('imagePreviewContainer');
        if (!container) return;

        const previewElement = document.createElement('div');
        previewElement.className = 'image-preview-item';
        previewElement.setAttribute('data-image-id', imageData.id);
        
        previewElement.innerHTML = `
            <div class="image-preview-wrapper">
                <img src="${imageData.base64}" alt="${imageData.name}" class="preview-image">
                <div class="image-overlay">
                    <div class="image-status" data-status="${imageData.status}">
                        <span class="status-icon">${this.getStatusIcon(imageData.status)}</span>
                        <span class="status-text">${this.getStatusText(imageData.status)}</span>
                    </div>
                    <div class="image-actions">
                        <button type="button" class="btn btn-icon btn-sm delete-image-btn" data-image-id="${imageData.id}" title="删除图片">
                            🗑️
                        </button>
                    </div>
                </div>
            </div>
            <div class="image-info">
                <div class="image-name">${imageData.name}</div>
                <div class="image-size">${this.formatFileSize(imageData.size)}</div>
            </div>
        `;

        container.appendChild(previewElement);

        // 绑定删除事件
        const deleteBtn = previewElement.querySelector('.delete-image-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => this.deleteImage(imageData.id));
        }
    }

    /**
     * 更新图片状态
     * @param {string} imageId - 图片ID
     * @param {string} status - 状态
     */
    updateImageStatus(imageId, status) {
        const previewItem = document.querySelector(`[data-image-id="${imageId}"]`);
        if (previewItem) {
            const statusElement = previewItem.querySelector('.image-status');
            if (statusElement) {
                statusElement.setAttribute('data-status', status);
                statusElement.querySelector('.status-icon').textContent = this.getStatusIcon(status);
                statusElement.querySelector('.status-text').textContent = this.getStatusText(status);
            }
        }
    }

    /**
     * 更新上传状态
     * @param {string} message - 状态消息
     * @param {string} type - 消息类型
     */
    updateUploadStatus(message, type = 'info') {
        const statusElement = document.getElementById('imageUploadStatus');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `upload-status ${type}`;
        }
    }

    /**
     * 删除图片
     * @param {string} imageId - 图片ID
     */
    deleteImage(imageId) {
        // 从数组中移除
        this.uploadedImages = this.uploadedImages.filter(img => img.id !== imageId);
        
        // 从DOM中移除
        const previewItem = document.querySelector(`[data-image-id="${imageId}"]`);
        if (previewItem) {
            previewItem.remove();
        }
        
        getLogger().log(`图片已删除: ${imageId}`, 'info');
    }

    /**
     * 获取状态图标
     * @param {string} status - 状态
     * @returns {string} 图标
     */
    getStatusIcon(status) {
        const icons = {
            uploading: '⏳',
            uploaded: '✅',
            analyzing: '🔍',
            analyzed: '📝',
            'no-text': '❓',
            error: '❌'
        };
        return icons[status] || '❓';
    }

    /**
     * 获取状态文本
     * @param {string} status - 状态
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const texts = {
            uploading: '上传中',
            uploaded: '已上传',
            analyzing: '分析中',
            analyzed: '已分析',
            'no-text': '无文本',
            error: '错误'
        };
        return texts[status] || '未知';
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 生成图片ID
     * @returns {string} 唯一ID
     */
    generateImageId() {
        return 'img_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 清空所有图片
     */
    clearAllImages() {
        this.uploadedImages = [];
        const container = document.getElementById('imagePreviewContainer');
        if (container) {
            container.innerHTML = '';
        }
        this.updateUploadStatus('', 'info');
    }

    /**
     * 获取上传的图片列表
     * @returns {Array} 图片列表
     */
    getUploadedImages() {
        return this.uploadedImages;
    }

    /**
     * 获取提取的文本内容
     * @returns {string} 合并的文本内容
     */
    getExtractedText() {
        return this.uploadedImages
            .filter(img => img.extractedText)
            .map(img => img.extractedText)
            .join('\n\n');
    }
}

// 创建全局实例
let imageUploadManagerInstance = null;

/**
 * 获取图片上传管理器实例
 * @returns {ImageUploadManager} 管理器实例
 */
function getImageUploadManager() {
    if (!imageUploadManagerInstance) {
        imageUploadManagerInstance = new ImageUploadManager();
    }
    return imageUploadManagerInstance;
}

// 导出到全局作用域
window.ImageUploadManager = ImageUploadManager;
window.getImageUploadManager = getImageUploadManager;
