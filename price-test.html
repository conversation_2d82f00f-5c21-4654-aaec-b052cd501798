<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>价格输入测试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-container {
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-primary, #fff);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-title {
            text-align: center;
            margin-bottom: 30px;
            color: var(--text-primary, #333);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-primary, #333);
        }
        .comparison {
            display: grid;
            gap: 20px;
            margin-top: 30px;
        }
        .old-style {
            padding: 20px;
            background: #f5f5f5;
            border-radius: 8px;
        }
        .new-style {
            padding: 20px;
            background: #e8f5e8;
            border-radius: 8px;
        }
        h3 {
            margin-top: 0;
            color: var(--text-primary, #333);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">价格输入组显示效果测试</h1>
        
        <div class="form-group">
            <label for="price1">价格输入 (90%数字输入 + 10%货币选择器 - 始终同一行)</label>
            <div class="price-input-group">
                <input type="number" id="price1" step="0.01" min="0" placeholder="输入价格" value="1299.50">
                <select id="currency1" title="选择货币">
                    <option value="MYR">MYR</option>
                    <option value="USD">USD</option>
                    <option value="SGD">SGD</option>
                    <option value="CNY">CNY</option>
                </select>
            </div>
        </div>
        
        <div class="form-group">
            <label for="price2">测试 - 不同价格值</label>
            <div class="price-input-group">
                <input type="number" id="price2" step="0.01" min="0" placeholder="测试长数字" value="123456789.99">
                <select id="currency2" title="选择货币">
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                    <option value="JPY">JPY</option>
                    <option value="AUD">AUD</option>
                </select>
            </div>
        </div>
        
        <div class="comparison">
            <div class="new-style">
                <h3>✅ 同一行显示优化：</h3>
                <ul>
                    <li>🚫 移除移动端的 flex-direction: column 设置</li>
                    <li>↔️ 强制使用 flex-direction: row 保持水平排列</li>
                    <li>� 添加 flex-wrap: nowrap 防止换行</li>
                    <li>📱 在768px以下屏幕仍保持同一行显示</li>
                    <li>📐 调整移动端边框圆角适应水平布局</li>
                    <li>� 优化内边距和字体大小适应小屏幕</li>
                    <li>📏 添加极小屏幕(480px)的适配规则</li>
                    <li>� 使用 box-sizing: border-box 确保尺寸准确</li>
                </ul>
                <p><strong>现在在任何屏幕尺寸下都不会换行！</strong></p>
            </div>
        </div>
        
        <div style="margin-top: 30px; text-align: center; color: var(--text-secondary, #666);">
            <p><strong>🎯 价格和货币选择器始终在同一行显示</strong></p>
            <p>✅ 桌面端：90% + 10% 水平布局</p>
            <p>📱 移动端：保持水平排列，自适应缩放</p>
            <p>📐 极小屏幕：最小化尺寸但不换行</p>
            <p>请尝试缩放浏览器窗口测试不同屏幕尺寸的效果</p>
        </div>
    </div>

    <script>
        // 简单的CSS变量fallback
        if (!CSS.supports('color', 'var(--test)')) {
            document.documentElement.style.setProperty('--bg-primary', '#ffffff');
            document.documentElement.style.setProperty('--text-primary', '#333333');
            document.documentElement.style.setProperty('--text-secondary', '#666666');
        }
    </script>
</body>
</html>
