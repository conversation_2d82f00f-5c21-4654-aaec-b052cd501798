
(function() {
    'use strict';

    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};

    /**
     * 账号到OTA配置的静态映射
     * - 键可以是用户ID (数字或字符串) 或 邮箱 (小写)
     * - 值是一个对象，包含默认渠道 `default` 和可选渠道列表 `options`
     */
    const otaChannelMapping = {
        // JR Coach (ID: 2666)
        2666: {
            default: 'JR Coach Credit',
            options: [
                { value: 'JR Coach Credit', text: 'JR Coach Credit' },
                { value: 'JR Coach Cash', text: 'JR Coach Cash' }
            ]
        },
        '<EMAIL>': {
            default: 'JR Coach Credit',
            options: [
                { value: 'JR Coach Credit', text: 'JR Coach Credit' },
                { value: 'JR Coach Cash', text: 'JR Coach Cash' }
            ]
        },

        // Super Admin (ID: 1)
        1: {
            default: 'Ctrip',
            options: [
                { value: 'Ctrip', text: '携程' },
                { value: 'Klook', text: 'Klook客路' },
                { value: 'KKday', text: 'KKday' }
            ]
        },
        '<EMAIL>': {
            default: 'Ctrip',
            options: [
                { value: 'Ctrip', text: '携程' },
                { value: 'Klook', text: 'Klook客路' },
                { value: 'KKday', text: 'KKday' }
            ]
        }
    };

    /**
     * 通用OTA渠道列表 (当没有特定用户配置时使用)
     */
    const commonChannels = [
        { value: 'Klook', text: 'Klook客路' },
        { value: 'KKday', text: 'KKday' },
        { value: 'Ctrip', text: '携程' },
        { value: 'Fliggy', text: '飞猪' },
        { value: 'Viator', text: 'Viator' },
        { value: 'Other', text: '其他' }
    ];

    /**
     * 获取指定用户ID或邮箱的OTA配置
     * @param {number|string} identifier - 用户ID或邮箱
     * @returns {object|null} 对应的OTA配置，未找到则返回null
     */
    function getConfig(identifier) {
        if (!identifier) return null;
        // 统一将标识符转为小写字符串，以匹配邮箱
        const key = typeof identifier === 'string' ? identifier.toLowerCase() : identifier;
        return otaChannelMapping[key] || null;
    }

    // 暴露到OTA命名空间
    window.OTA.otaChannelMapping = {
        getConfig,
        commonChannels
    };

})();