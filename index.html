<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA订单处理系统 - GoMyHire Integration</title>
    <link rel="stylesheet" href="style.css">
    <!-- 图标已移除，使用浏览器默认图标 -->
</head>
<body>
    <div id="app">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="title-icon">🚗</span>
                    <span data-i18n="header.title">OTA订单处理系统</span>
                </h1>
                <div class="header-controls">
                    <div class="persistent-email" id="persistentEmailContainer" style="display: none;">
                        <label for="persistentEmail" data-i18n="header.defaultEmail">默认邮箱:</label>
                        <input type="email" id="persistentEmail" data-i18n="header.defaultEmailPlaceholder" placeholder="设置默认客户邮箱" data-i18n-title="header.defaultEmailTooltip" title="设置默认客户邮箱，当AI解析无法获取邮箱时自动使用">
                        <button type="button" id="saveEmailBtn" class="btn btn-icon" data-i18n-title="common.save" title="保存邮箱">💾</button>
                    </div>
                    <div class="user-info" id="userInfo" style="display: none;">
                        <span id="currentUser"></span>
                        <button type="button" id="historyBtn" class="btn btn-outline" data-i18n="header.historyOrders">历史订单</button>
                        <button type="button" id="logoutBtn" class="btn btn-outline" data-i18n="header.logout">退出登录</button>
                    </div>
                    <div class="theme-toggle">
                        <select id="languageSelect" class="language-select" data-i18n-title="header.language" title="选择语言">
                            <option value="zh" data-i18n="header.languageZh">中文</option>
                            <option value="en" data-i18n="header.languageEn">English</option>
                        </select>
                        <button type="button" id="themeToggle" class="btn btn-icon" data-i18n-title="header.toggleTheme" title="切换主题">🌙</button>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容区 -->
        <main class="main-content">
            <!-- 登录面板 -->
            <div id="loginPanel" class="login-panel">
                <div class="login-card">
                    <h2 data-i18n="login.title">系统登录</h2>
                    <form id="loginForm">
                        <div class="form-group">
                            <label for="email" data-i18n="login.email">邮箱</label>
                            <input type="email" id="email" value="" data-i18n="login.emailPlaceholder" placeholder="请输入邮箱地址" required>
                        </div>
                        <div class="form-group">
                            <label for="password" data-i18n="login.password">密码</label>
                            <input type="password" id="password" value="" data-i18n="login.passwordPlaceholder" placeholder="请输入密码" required>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="rememberMe" checked>
                                <span class="checkbox-text" data-i18n="login.rememberMe">保持登录</span>
                            </label>
                        </div>
                        <div class="login-actions">
                            <button type="submit" class="btn btn-primary" id="loginBtn">
                                <span class="btn-text" data-i18n="login.loginButton">登录</span>
                                <span class="loading-spinner hidden">⏳</span>
                            </button>
                            <button type="button" class="btn btn-outline btn-sm hidden" id="clearSavedBtn" data-i18n="login.clearSaved">
                                清除保存的账号
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 工作区 -->
            <div id="workspace" class="workspace" style="display: none;">
                <!-- 田字格布局容器 -->
                <form id="orderForm" class="grid-container-new">
                    <!-- 左列 -->
                    <div class="column-left">
                        <!-- 订单输入板块 -->
                        <section class="panel" data-panel="order-input" role="region" aria-label="订单输入区域" tabindex="0">
                            <div class="resize-handle resize-handle-bottom" role="separator" aria-label="拖拽调整订单输入区域高度" aria-orientation="horizontal"></div>

                            <div class="section-header">
                                <h3 data-i18n="input.title">📝 订单输入</h3>
                                <div class="section-controls">
                                    <button type="button" id="clearInput" class="btn btn-outline btn-sm" data-i18n="input.clearButton">清空</button>
                                    <button type="button" id="sampleInput" class="btn btn-outline btn-sm" data-i18n="input.sampleButton">示例数据</button>
                                    <button type="button" id="parseBtn" class="btn btn-secondary btn-sm" data-i18n="input.parseButton" title="手动解析（备用）">手动解析</button>
                                </div>
                            </div>
                            <div class="panel-content">
                                <div class="form-group">
                                    <label for="orderInput" data-i18n="input.orderDescription">订单描述</label>
                                    <div class="textarea-container">
                                        <textarea
                                            id="orderInput"
                                            placeholder=""
                                            rows="4"
                                        ></textarea>
                                        <button type="button" class="textarea-upload-button" id="imageUploadButton" title="上传图片">
                                            <span class="upload-icon">📁</span>
                                        </button>
                                        <input type="file" id="imageFileInput" accept="image/jpeg,image/jpg,image/png,image/webp" multiple style="display: none;">
                                    </div>
                                </div>
                                <div id="imageUploadStatus" class="upload-status"></div>
                                <div id="imagePreviewContainer" class="image-preview-container"></div>
                            </div>
                        </section>

                        <!-- 行程信息板块 -->
                        <section class="panel" data-panel="trip-info" role="region" aria-label="行程信息区域" tabindex="0">
                            <div class="resize-handle resize-handle-bottom" role="separator" aria-label="拖拽调整行程信息区域高度" aria-orientation="horizontal"></div>

                            <div class="section-header">
                                <h3 data-i18n="form.tripInfo">🚗 行程信息</h3>
                            </div>
                            <div class="panel-content">
                                <div class="form-group">
                                    <label for="pickup" data-i18n="form.pickup">上车地点</label>
                                    <input type="text" id="pickup" data-i18n="form.pickupPlaceholder" placeholder="上车地点" data-i18n-title="form.pickupTooltip" title="客户上车的地点">
                                </div>

                                <div class="form-group">
                                    <label for="dropoff" data-i18n="form.dropoff">目的地</label>
                                    <input type="text" id="dropoff" data-i18n="form.dropoffPlaceholder" placeholder="目的地" data-i18n-title="form.dropoffTooltip" title="客户的目的地">
                                </div>

                                <div class="form-group">
                                    <label for="pickupDate" data-i18n="form.pickupDate">接送日期</label>
                                    <input type="date" id="pickupDate" data-i18n-title="form.pickupDateTooltip" title="接送日期">
                                </div>

                                <div class="form-group">
                                    <label for="pickupTime" data-i18n="form.pickupTime">接送时间</label>
                                    <input type="time" id="pickupTime" data-i18n-title="form.pickupTimeTooltip" title="接送时间">
                                </div>
                            </div>
                        </section>

                        <!-- 特殊需求板块 -->
                        <section class="panel" data-panel="special-requirements" role="region" aria-label="特殊需求区域" tabindex="0">
                            <div class="resize-handle resize-handle-bottom" role="separator" aria-label="拖拽调整特殊需求区域高度" aria-orientation="horizontal"></div>

                            <div class="section-header">
                                <h3 data-i18n="form.specialRequirements">🔧 特殊需求</h3>
                            </div>
                            <div class="panel-content">
                                <div class="form-group">
                                    <label data-i18n="form.specialRequirements">特殊要求选项</label>
                                    <div class="checkbox-group-vertical">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="babyChairMain">
                                            <span data-i18n="form.babyChair">儿童座椅</span>
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="tourGuideMain">
                                            <span data-i18n="form.tourGuide">导游服务</span>
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="meetAndGreetMain">
                                            <span data-i18n="form.meetAndGreet">迎接服务</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <!-- 额外要求板块 - 从右列移动到左列 -->
                        <section class="panel" data-panel="extra-requirements" role="region" aria-label="额外要求区域" tabindex="0">
                            <div class="section-header">
                                <h3 data-i18n="form.extraRequirement">📝 额外要求</h3>
                            </div>
                            <div class="panel-content">
                                <div class="form-group">
                                    <label for="extraRequirement" data-i18n="form.extraRequirement">额外要求</label>
                                    <textarea id="extraRequirement" rows="4" placeholder="" data-i18n-title="form.extraRequirementTooltip" title="其他特殊要求或备注"></textarea>
                                </div>
                            </div>
                        </section>
                    </div>

                    <div class="column-right">
                        <!-- 基本信息板块 -->
                        <section class="panel" data-panel="basic-info" role="region" aria-label="基本信息区域" tabindex="0">
                            <div class="resize-handle resize-handle-bottom" role="separator" aria-label="拖拽调整基本信息区域高度" aria-orientation="horizontal"></div>

                            <div class="section-header">
                                <h3 data-i18n="form.basicInfo">📋 基本信息</h3>
                            </div>
                            <div class="panel-content">
                                <div class="form-group">
                                    <label for="subCategoryId" data-i18n="form.serviceType">服务类型</label>
                                    <select id="subCategoryId" data-i18n="form.serviceTypePlaceholder" data-i18n-title="form.serviceTypeTooltip" title="选择服务类型">
                                        <option value="" data-i18n="form.selectServiceType">请选择服务类型</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="otaReferenceNumber" data-i18n="form.otaReference">OTA参考号</label>
                                    <input type="text" id="otaReferenceNumber" data-i18n="form.otaReferencePlaceholder" placeholder="OTA平台订单号" data-i18n-title="form.otaReferenceTooltip" title="OTA平台的订单参考号">
                                </div>

                                <div class="form-group">
                                    <label for="ota" data-i18n="form.otaChannel">OTA 渠道名字</label>
                                    <select id="ota" data-i18n="form.otaChannelPlaceholder" data-i18n-title="form.otaChannelTooltip" title="选择OTA渠道">
                                        <option value="" data-i18n="form.selectOtaChannel">请选择OTA渠道</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="carTypeId" data-i18n="form.carType">车型</label>
                                    <select id="carTypeId" data-i18n="form.carTypePlaceholder" data-i18n-title="form.carTypeTooltip" title="选择车型">
                                        <option value="" data-i18n="form.selectCarType">请选择车型</option>
                                    </select>
                                </div>
                            </div>
                        </section>

                        <!-- 客户信息板块 -->
                        <section class="panel" data-panel="customer-info" role="region" aria-label="客户信息区域" tabindex="0">
                            <div class="resize-handle resize-handle-bottom" role="separator" aria-label="拖拽调整客户信息区域高度" aria-orientation="horizontal"></div>

                            <div class="section-header">
                                <h3>👤 客户信息</h3>
                            </div>
                            <div class="panel-content">
                                <div class="form-group">
                                    <label for="customerName" data-i18n="form.customerName">客户姓名</label>
                                    <input type="text" id="customerName" data-i18n="form.customerNamePlaceholder" placeholder="客户姓名" data-i18n-title="form.customerNameTooltip" title="客户的姓名">
                                </div>

                                <div class="form-group">
                                    <label for="customerContact" data-i18n="form.customerPhone">联系电话</label>
                                    <input type="tel" id="customerContact" data-i18n="form.customerPhonePlaceholder" placeholder="联系电话" data-i18n-title="form.customerPhoneTooltip" title="客户的联系电话">
                                </div>

                                <div class="form-group">
                                    <label for="customerEmail" data-i18n="form.customerEmail">客户邮箱</label>
                                    <input type="email" id="customerEmail" data-i18n="form.customerEmailPlaceholder" placeholder="客户邮箱" data-i18n-title="form.customerEmailTooltip" title="客户的邮箱地址">
                                </div>

                                <div class="form-group">
                                    <label for="flightInfo" data-i18n="form.flightInfo">航班信息</label>
                                    <input type="text" id="flightInfo" data-i18n="form.flightInfoPlaceholder" placeholder="航班号/航班信息" data-i18n-title="form.flightInfoTooltip" title="航班号或相关航班信息">
                                </div>
                            </div>
                        </section>

                        <!-- 服务配置板块 -->
                        <section class="panel" data-panel="service-config" role="region" aria-label="服务配置区域" tabindex="0">
                            <div class="section-header">
                                <h3 data-i18n="form.serviceConfig">⚙️ 服务配置</h3>
                            </div>
                            <div class="panel-content">
                                <div class="form-group">
                                    <label for="passengerCount" data-i18n="form.passengerCount">乘客人数</label>
                                    <input type="number" id="passengerCount" min="1" max="20" data-i18n="form.passengerCountPlaceholder" placeholder="乘客人数" data-i18n-title="form.passengerCountTooltip" title="乘客人数">
                                </div>

                                <div class="form-group">
                                    <label for="luggageCount" data-i18n="form.luggageCount">行李件数</label>
                                    <input type="number" id="luggageCount" min="0" max="50" data-i18n="form.luggageCountPlaceholder" placeholder="行李件数" data-i18n-title="form.luggageCountTooltip" title="行李件数">
                                </div>

                                <div class="form-group">
                                    <label for="drivingRegionId" data-i18n="form.drivingRegion">行驶区域</label>
                                    <select id="drivingRegionId" data-i18n="form.drivingRegionPlaceholder" data-i18n-title="form.drivingRegionTooltip" title="选择行驶区域">
                                        <option value="" data-i18n="form.selectDrivingRegion">请选择行驶区域</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="languagesIdArray" data-i18n="form.languages">语言要求</label>
                                    <div class="multi-select-dropdown" id="languagesDropdown">
                                        <div class="multi-select-trigger" id="languagesTrigger" tabindex="0" role="combobox" aria-expanded="false" aria-haspopup="listbox" data-i18n-title="form.languagesTooltip" title="选择语言要求">
                                            <span class="multi-select-text" data-i18n="form.selectLanguages">请选择语言</span>
                                            <span class="multi-select-arrow">▼</span>
                                        </div>
                                        <div class="multi-select-options" id="languagesOptions" role="listbox" aria-label="语言选项">
                                            <!-- 选项将通过JavaScript动态生成 -->
                                        </div>
                                        <!-- 隐藏的原始select元素，保持表单兼容性 -->
                                        <select id="languagesIdArray" multiple data-i18n="form.languagesPlaceholder" data-i18n-title="form.languagesTooltip" title="选择语言要求">
                                            <option value="" data-i18n="form.selectLanguages">请选择语言</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="otaPrice" data-i18n="form.price">价格</label>
                                    <div class="price-input-group">
                                        <input type="number" id="otaPrice" step="0.01" min="0" data-i18n="form.pricePlaceholder" placeholder="价格" data-i18n-title="form.priceTooltip" title="订单价格">
                                        <select id="currency" data-i18n="form.currencyPlaceholder" data-i18n-title="form.currencyTooltip" title="选择货币">
                                            <option value="MYR">MYR</option>
                                            <option value="USD">USD</option>
                                            <option value="SGD">SGD</option>
                                            <option value="CNY">CNY</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>

                    <!-- 操作按钮区域 - 移入grid内部，跨越两列 -->
                    <section class="action-section grid-span-full">
                        <div class="form-actions">
                            <button type="button" id="returnToMultiOrder" class="btn btn-secondary hidden" data-i18n="multiOrder.returnToMultiOrder">
                                <span>🔢 返回多订单模式</span>
                            </button>
                            <button type="button" id="validateData" class="btn btn-outline">
                                <span>⚠️ 提示数据异常</span>
                            </button>
                            <button type="button" id="createOrder" class="btn btn-primary">
                                <span>✅ 创建订单</span>
                            </button>
                        </div>
                    </section>
                </form>





                <!-- 日志控制台 -->
                <!-- 已移除日志控制台前端显示，仅保留后台调试控制台输出 -->
            </div>
        </main>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <div class="status-info">
                <span id="connectionStatus" class="status-item">🔌 未连接</span>
                <span id="dataStatus" class="status-item">📊 等待数据</span>
                <span id="lastUpdate" class="status-item">⏰ --:--</span>
            </div>
        </footer>

        <!-- 模态框 -->
        <div id="modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">标题</h3>
                    <button type="button" id="modalClose" class="btn btn-icon">✕</button>
                </div>
                <div id="modalBody" class="modal-body">内容</div>
                <div class="modal-footer">
                    <button type="button" id="modalCancel" class="btn btn-outline">取消</button>
                    <button type="button" id="modalConfirm" class="btn btn-primary">确认</button>
                </div>
            </div>
        </div>

        <!-- 历史订单面板 -->
        <div id="historyPanel" class="history-panel hidden">
            <div class="history-overlay">
                <div class="history-content">
                    <div class="history-header">
                        <h3>📋 历史订单管理</h3>
                        <div class="history-controls">
                            <button type="button" id="exportHistoryBtn" class="btn btn-outline btn-sm">导出</button>
                            <button type="button" id="clearHistoryBtn" class="btn btn-outline btn-sm">清空</button>
                            <button type="button" id="closeHistoryBtn" class="btn btn-icon">✕</button>
                        </div>
                    </div>

                    <div class="history-search">
                        <div class="search-grid">
                            <div class="search-group">
                                <label for="searchOrderId">订单ID</label>
                                <input type="text" id="searchOrderId" placeholder="搜索订单ID">
                            </div>
                            <div class="search-group">
                                <label for="searchCustomer">客户姓名</label>
                                <input type="text" id="searchCustomer" placeholder="搜索客户姓名">
                            </div>
                            <div class="search-group">
                                <label for="searchDateFrom">开始日期</label>
                                <input type="date" id="searchDateFrom">
                            </div>
                            <div class="search-group">
                                <label for="searchDateTo">结束日期</label>
                                <input type="date" id="searchDateTo">
                            </div>
                        </div>
                        <div class="search-actions">
                            <button type="button" id="searchHistoryBtn" class="btn btn-primary btn-sm">搜索</button>
                            <button type="button" id="resetSearchBtn" class="btn btn-outline btn-sm">重置</button>
                        </div>
                    </div>

                    <div class="history-stats">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-label">总计</span>
                                <span class="stat-value" id="statTotal">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">今日</span>
                                <span class="stat-value" id="statToday">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">本周</span>
                                <span class="stat-value" id="statWeek">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">本月</span>
                                <span class="stat-value" id="statMonth">0</span>
                            </div>
                        </div>
                    </div>

                    <div class="history-list">
                        <div class="list-header">
                            <div class="list-title">订单列表</div>
                            <div class="list-count">共 <span id="listCount">0</span> 条记录</div>
                        </div>
                        <div class="list-container" id="historyListContainer">
                            <div class="empty-state">
                                <div class="empty-icon">📝</div>
                                <div class="empty-text">暂无历史订单</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 多订单预览面板 -->
        <div id="multiOrderPanel" class="multi-order-panel hidden">
            <div class="multi-order-overlay">
                <div class="multi-order-content">
                    <div class="multi-order-header">
                        <h3 data-i18n="multiOrder.title">🔢 多订单预览与编辑</h3>
                        <div class="multi-order-controls">
                            <div class="order-stats">
                                <span id="multiOrderCount" class="order-count">0 个订单</span>
                                <span id="multiOrderDateRange" class="date-range"></span>
                            </div>
                            <div class="header-actions">
                                <button type="button" id="batchCreateBtn" class="btn btn-primary btn-sm" data-i18n="multiOrder.batchCreate">批量创建</button>
                                <button type="button" id="closeMultiOrderBtn" class="btn btn-icon" data-i18n-title="common.close">✕</button>
                            </div>
                        </div>
                    </div>

                    <div class="multi-order-list" id="multiOrderList">
                        <!-- 多订单项将在这里动态生成 -->
                    </div>

                    <!-- 批量创建进度显示 -->
                    <div class="batch-create-status"></div>

                    <div class="multi-order-footer">
                        <div class="batch-actions">
                            <button type="button" id="selectAllOrdersBtn" class="btn btn-outline btn-sm" data-i18n="multiOrder.selectAll">全选</button>
                            <button type="button" id="deselectAllOrdersBtn" class="btn btn-outline btn-sm" data-i18n="multiOrder.deselectAll">取消全选</button>
                            <button type="button" id="validateAllOrdersBtn" class="btn btn-outline btn-sm" data-i18n="multiOrder.validateAll">验证全部</button>
                        </div>
                        <div class="creation-summary">
                            <span id="selectedOrderCount">已选择 0 个订单</span>
                            <button type="button" id="createSelectedOrdersBtn" class="btn btn-primary" data-i18n="multiOrder.createSelected">创建选中订单</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript模块 - 传统script标签加载方式 -->
    <!-- 注意：加载顺序很重要，依赖的模块必须先加载 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/ota-channel-mapping.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/order-history-manager.js"></script>
    <script src="js/image-upload-manager.js"></script>
    <script src="js/currency-converter.js"></script>
    <script src="js/multi-order-manager.js"></script>
    <script src="js/paging-service-manager.js"></script>
    <script src="js/multi-select-dropdown.js"></script>
    <script src="js/grid-resizer.js"></script>
    <script src="js/i18n.js"></script>

    <!-- 管理器模块 - 必须在ui-manager.js之前加载 -->
    <script src="js/managers/form-manager.js"></script>
    <script src="js/managers/price-manager.js"></script>
    <script src="js/managers/event-manager.js"></script>
    <script src="js/managers/state-manager.js"></script>
    <script src="js/managers/realtime-analysis-manager.js"></script>

    <!-- UI管理器 - 作为协调器最后加载 -->
    <script src="js/ui-manager.js"></script>
    <script src="main.js"></script>
</body>
</html>